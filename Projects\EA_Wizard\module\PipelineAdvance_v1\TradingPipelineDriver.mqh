#property strict

#include "TradingEvent.mqh"
#include "TradingPipeline.mqh"  // 首先包含 PipelineResult 定義
#include "interface/ITradingPipelineDriver.mqh"
#include "TradingPipelineContainerManager.mqh"
#include "TradingPipelineRegistry.mqh"
#include "TradingPipelineExplorer.mqh"

//+------------------------------------------------------------------+
//| 常量定義                                                         |
//+------------------------------------------------------------------+
#define TRADING_PIPELINE_DRIVER_NAME "TradingPipelineDriver"
#define TRADING_PIPELINE_DRIVER_TYPE "PipelineDriver"
#define DEFAULT_MAX_CONTAINERS 20
#define DEFAULT_MAX_REGISTRATIONS 100

//+------------------------------------------------------------------+
//| 交易流水線驅動器 - 單例模式                                     |
//| 負責初始化和控制主要模組                                         |
//+------------------------------------------------------------------+
class TradingPipelineDriver : public ITradingPipelineDriver
{
private:
    static TradingPipelineDriver* s_instance;           // 單例實例

    // 核心組件
    TradingPipelineContainerManager* m_manager;         // 容器管理器
    TradingPipelineRegistry* m_registry;                // 註冊器
    TradingPipelineExplorer* m_explorer;                // 探索器

    // 狀態管理
    bool m_isInitialized;                               // 是否已初始化
    string m_name;                                      // 驅動器名稱
    string m_type;                                      // 驅動器類型
    PipelineResult* m_last_result;                      // 執行結果

    //+------------------------------------------------------------------+
    //| 私有構造函數（單例模式）                                         |
    //+------------------------------------------------------------------+
    TradingPipelineDriver()
        : m_manager(NULL),
          m_registry(NULL),
          m_explorer(NULL),
          m_isInitialized(false),
          m_name(TRADING_PIPELINE_DRIVER_NAME),
          m_type(TRADING_PIPELINE_DRIVER_TYPE),
          m_last_result(new PipelineResult(false, "驅動器尚未初始化", TRADING_PIPELINE_DRIVER_NAME, ERROR_LEVEL_INFO))
    {
        // 執行初始化
        if(Initialize())
        {
            m_isInitialized = true;
            m_last_result = new PipelineResult(true, "驅動器初始化成功", GetName(), ERROR_LEVEL_INFO);
        }
        else
        {
            m_last_result = new PipelineResult(false, "驅動器初始化失敗", GetName(), ERROR_LEVEL_ERROR);
            Cleanup();
        }
    }

public:
    //+------------------------------------------------------------------+
    //| 獲取單例實例                                                     |
    //+------------------------------------------------------------------+
    static TradingPipelineDriver* GetInstance()
    {
        if(s_instance == NULL)
        {
            s_instance = new TradingPipelineDriver();
        }
        return s_instance;
    }

    //+------------------------------------------------------------------+
    //| 析構函數                                                         |
    //+------------------------------------------------------------------+
    virtual ~TradingPipelineDriver()
    {
        Cleanup();
        if(m_last_result != NULL)
        {
            delete m_last_result;
            m_last_result = NULL;
        }
    }

    //+------------------------------------------------------------------+
    //| 釋放單例實例                                                     |
    //+------------------------------------------------------------------+
    static void ReleaseInstance()
    {
        if(s_instance != NULL)
        {
            delete s_instance;
            s_instance = NULL;
        }
    }

    //+------------------------------------------------------------------+
    //| 初始化所有組件                                                   |
    //+------------------------------------------------------------------+
    bool Initialize()
    {
        // 1. 創建容器管理器
        if(!InitializeManager())
        {
            m_last_result = new PipelineResult(false, "容器管理器初始化失敗", GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        // 2. 創建註冊器（依賴管理器）
        if(!InitializeRegistry())
        {
            m_last_result = new PipelineResult(false, "註冊器初始化失敗", GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        // 3. 創建探索器（依賴註冊器）
        if(!InitializeExplorer())
        {
            m_last_result = new PipelineResult(false, "探索器初始化失敗", GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        // 4. 創建默認容器
        if(!SetupDefaultConfiguration())
        {
            m_last_result = new PipelineResult(false, "默認配置設置失敗", GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        m_last_result = new PipelineResult(true, "所有組件初始化完成", GetName(), ERROR_LEVEL_INFO);
        return true;
    }

    //+------------------------------------------------------------------+
    //| 清理所有組件                                                     |
    //+------------------------------------------------------------------+
    void Cleanup()
    {
        // 按相反順序清理組件
        if(m_explorer != NULL)
        {
            delete m_explorer;
            m_explorer = NULL;
        }

        if(m_registry != NULL)
        {
            delete m_registry;
            m_registry = NULL;
        }

        if(m_manager != NULL)
        {
            delete m_manager;
            m_manager = NULL;
        }

        m_isInitialized = false;
    }

    //+------------------------------------------------------------------+
    //| 訪問器方法                                                       |
    //+------------------------------------------------------------------+
    TradingPipelineContainerManager* GetManager() const
    {
        return m_manager;
    }

    TradingPipelineRegistry* GetRegistry() const
    {
        return m_registry;
    }

    TradingPipelineExplorer* GetExplorer() const
    {
        return m_explorer;
    }

    //+------------------------------------------------------------------+
    //| 狀態查詢方法                                                     |
    //+------------------------------------------------------------------+
    bool IsInitialized() const
    {
        return m_isInitialized &&
               m_manager != NULL &&
               m_registry != NULL &&
               m_explorer != NULL;
    }

    string GetName() const
    {
        return m_name;
    }

    string GetType() const
    {
        return m_type;
    }

    // 獲取執行結果
    PipelineResult* GetResult() const
    {
        return m_last_result;
    }

    //+------------------------------------------------------------------+
    //| 狀態報告                                                         |
    //+------------------------------------------------------------------+
    string GetStatusReport() const
    {
        string report = "=== TradingPipelineDriver 狀態報告 ===\n";
        report += "驅動器名稱: " + m_name + "\n";
        report += "驅動器類型: " + m_type + "\n";
        report += "初始化狀態: " + (m_isInitialized ? "已初始化" : "未初始化") + "\n";
        report += "容器管理器: " + (m_manager != NULL ? "正常" : "NULL") + "\n";
        report += "註冊器: " + (m_registry != NULL ? "正常" : "NULL") + "\n";
        report += "探索器: " + (m_explorer != NULL ? "正常" : "NULL") + "\n";

        if(IsInitialized())
        {
            report += "\n=== 組件統計信息 ===\n";
            report += "容器數量: " + IntegerToString(GetTotalContainers()) + "\n";
            report += "註冊數量: " + IntegerToString(GetTotalRegistrations()) + "\n";
        }

        return report;
    }

    //+------------------------------------------------------------------+
    //| 組件統計信息                                                     |
    //+------------------------------------------------------------------+
    int GetTotalContainers() const
    {
        return (m_manager != NULL) ? m_manager.GetContainerCount() : 0;
    }

    int GetTotalRegistrations() const
    {
        return (m_registry != NULL) ? m_registry.GetTotalRegistrations() : 0;
    }

    //+------------------------------------------------------------------+
    //| 重置功能                                                         |
    //+------------------------------------------------------------------+
    bool Reset()
    {
        // 先設置為未初始化狀態
        m_isInitialized = false;

        // 清理現有組件
        Cleanup();

        // 重新初始化
        bool initResult = Initialize();

        if(initResult)
        {
            m_last_result = new PipelineResult(true, "驅動器重置成功", GetName(), ERROR_LEVEL_INFO);
        }
        else
        {
            m_last_result = new PipelineResult(false, "驅動器重置失敗", GetName(), ERROR_LEVEL_ERROR);
        }

        return initResult;
    }

    //+------------------------------------------------------------------+
    //| 設置默認配置                                                     |
    //+------------------------------------------------------------------+
    bool SetupDefaultConfiguration()
    {
        // 移除 IsInitialized() 檢查，因為此方法在初始化過程中被調用
        // 此時 m_isInitialized 尚未設置為 true
        if(m_manager == NULL || m_registry == NULL || m_explorer == NULL)
        {
            m_last_result = new PipelineResult(false, "核心組件未初始化，無法設置默認配置", GetName(), ERROR_LEVEL_ERROR);
            return false;
        }

        // 創建默認的事件容器
        bool success = true;

        // 為每個事件創建容器
        success &= CreateDefaultStageContainer(TRADING_INIT, "初始化容器");
        success &= CreateDefaultStageContainer(TRADING_TICK, "Tick處理容器");
        success &= CreateDefaultStageContainer(TRADING_DEINIT, "清理容器");

        // 為每個階段創建對應的容器（如果需要的話）
        success &= CreateDefaultStageContainerForStage(INIT_START, "初始化開始容器");
        success &= CreateDefaultStageContainerForStage(INIT_COMPLETE, "初始化完成容器");
        success &= CreateDefaultStageContainerForStage(TICK_DATA_FEED, "數據饋送容器");

        if(success)
        {
            m_last_result = new PipelineResult(true, "默認配置設置成功", GetName(), ERROR_LEVEL_INFO);
        }
        else
        {
            m_last_result = new PipelineResult(false, "默認配置設置部分失敗", GetName(), ERROR_LEVEL_WARNING);
        }

        return success;
    }

    //+------------------------------------------------------------------+
    //| 獲取組件詳細信息                                                 |
    //+------------------------------------------------------------------+
    string GetComponentInfo() const
    {
        string info = "=== TradingPipelineDriver 組件信息 ===\n";

        if(m_manager != NULL)
        {
            info += "容器管理器:\n";
            info += "  - 名稱: " + m_manager.GetName() + "\n";
            info += "  - 類型: " + m_manager.GetType() + "\n";
            info += "  - 容器數量: " + IntegerToString(m_manager.GetContainerCount()) + "\n";
            info += "  - 最大容器數: " + IntegerToString(m_manager.GetMaxContainers()) + "\n";
            info += "  - 啟用狀態: " + (m_manager.IsEnabled() ? "啟用" : "禁用") + "\n";
        }

        if(m_registry != NULL)
        {
            info += "\n註冊器:\n";
            info += "  - 名稱: " + m_registry.GetName() + "\n";
            info += "  - 類型: " + m_registry.GetType() + "\n";
            info += "  - 總註冊數: " + IntegerToString(m_registry.GetTotalRegistrations()) + "\n";
            info += "  - 最大註冊數: " + IntegerToString(m_registry.GetMaxRegistrations()) + "\n";
            info += "  - 啟用狀態: " + (m_registry.IsEnabled() ? "啟用" : "禁用") + "\n";
        }

        if(m_explorer != NULL)
        {
            info += "\n探索器:\n";
            info += "  - 名稱: " + m_explorer.GetName() + "\n";
            info += "  - 類型: " + m_explorer.GetType() + "\n";
            info += "  - 描述: " + m_explorer.GetDescription() + "\n";
            info += "  - 有效狀態: " + (m_explorer.IsValid() ? "有效" : "無效") + "\n";
        }

        return info;
    }

    //+------------------------------------------------------------------+
    //| 驗證所有組件狀態                                                 |
    //+------------------------------------------------------------------+
    bool ValidateComponents() const
    {
        bool isValid = true;
        string errorMessages = "";

        if(m_manager == NULL)
        {
            errorMessages += "容器管理器為NULL; ";
            isValid = false;
        }

        if(m_registry == NULL)
        {
            errorMessages += "註冊器為NULL; ";
            isValid = false;
        }

        if(m_explorer == NULL)
        {
            errorMessages += "探索器為NULL; ";
            isValid = false;
        }

        if(isValid && m_explorer != NULL && !m_explorer.IsValid())
        {
            errorMessages += "探索器狀態無效; ";
            isValid = false;
        }

        // 更新結果（注意：這是 const 方法，所以不能修改 m_last_result）
        // 如果需要記錄驗證結果，可以考慮將此方法改為非 const

        return isValid;
    }

private:


    //+------------------------------------------------------------------+
    //| 初始化容器管理器                                                 |
    //+------------------------------------------------------------------+
    bool InitializeManager()
    {
        m_manager = new TradingPipelineContainerManager(
            "MainContainerManager",
            "ContainerManager",
            true,  // owned
            DEFAULT_MAX_CONTAINERS
        );

        if(m_manager == NULL)
        {
            return false;
        }

        return true;
    }

    //+------------------------------------------------------------------+
    //| 初始化註冊器                                                     |
    //+------------------------------------------------------------------+
    bool InitializeRegistry()
    {
        if(m_manager == NULL)
        {
            return false;
        }

        m_registry = new TradingPipelineRegistry(
            m_manager,
            "MainRegistry",
            "PipelineRegistry",
            DEFAULT_MAX_REGISTRATIONS,
            true  // owned
        );

        if(m_registry == NULL)
        {
            return false;
        }

        return true;
    }

    //+------------------------------------------------------------------+
    //| 初始化探索器                                                     |
    //+------------------------------------------------------------------+
    bool InitializeExplorer()
    {
        if(m_registry == NULL)
        {
            return false;
        }

        m_explorer = new TradingPipelineExplorer(
            m_registry,
            "MainExplorer",
            "TradingPipelineExplorer",
            "主要交易流水線探索器"
        );

        if(m_explorer == NULL)
        {
            return false;
        }

        return true;
    }

    //+------------------------------------------------------------------+
    //| 創建默認階段容器                                                 |
    //+------------------------------------------------------------------+
    bool CreateDefaultStageContainer(ENUM_TRADING_EVENT event, string description)
    {
        if(m_registry == NULL)
        {
            return false;
        }

        // 創建容器名稱
        string containerName = "Container_" + TradingEventUtils::EventToString(event);

        // 創建容器
        TradingPipelineContainer* container = new TradingPipelineContainer(
            containerName,
            description,
            "TradingPipelineContainer",
            true,  // owned
            10     // maxPipelines
        );

        if(container == NULL)
        {
            return false;
        }

        // 註冊容器
        bool registered = m_registry.Register(event, container);
        if(!registered)
        {
            delete container;
            return false;
        }

        return true;
    }

    //+------------------------------------------------------------------+
    //| 為特定階段創建默認容器                                           |
    //+------------------------------------------------------------------+
    bool CreateDefaultStageContainerForStage(ENUM_TRADING_STAGE stage, string description)
    {
        if(m_registry == NULL)
        {
            return false;
        }

        // 創建容器名稱
        string containerName = "Container_" + TradingEventUtils::StageToString(stage);

        // 創建容器
        TradingPipelineContainer* container = new TradingPipelineContainer(
            containerName,
            description,
            "TradingPipelineContainer",
            true,  // owned
            10     // maxPipelines
        );

        if(container == NULL)
        {
            return false;
        }

        // 註冊容器到階段
        bool registered = m_registry.Register(stage, container);
        if(!registered)
        {
            delete container;
            return false;
        }

        return true;
    }
};

//+------------------------------------------------------------------+
//| 靜態成員初始化                                                   |
//+------------------------------------------------------------------+
TradingPipelineDriver* TradingPipelineDriver::s_instance = NULL;
